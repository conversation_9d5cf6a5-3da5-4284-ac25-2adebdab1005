# -*- mode: python ; coding: utf-8 -*-
import os
import sys
from PyInstaller.utils.hooks import collect_data_files, collect_submodules

# 获取项目根目录
project_root = os.path.dirname(os.path.abspath(SPEC))

# 收集所有需要的数据文件
datas = []

# 添加静态文件
datas += [
    (os.path.join(project_root, 'app', 'static'), 'app/static'),
    (os.path.join(project_root, 'app', 'templates'), 'app/templates'),
]

# 添加数据库文件（如果存在）
db_path = os.path.join(project_root, 'data', 'gemini_balance.db')
if os.path.exists(db_path):
    datas += [(db_path, 'data')]

# 收集第三方库的数据文件
datas += collect_data_files('fastapi')
datas += collect_data_files('starlette')
datas += collect_data_files('uvicorn')
datas += collect_data_files('jinja2')

# 隐藏导入的模块
hiddenimports = []
hiddenimports += collect_submodules('uvicorn')
hiddenimports += collect_submodules('fastapi')
hiddenimports += collect_submodules('starlette')
hiddenimports += collect_submodules('pydantic')
hiddenimports += collect_submodules('sqlalchemy')
hiddenimports += collect_submodules('aiosqlite')
hiddenimports += collect_submodules('aiomysql')
hiddenimports += collect_submodules('pymysql')
hiddenimports += collect_submodules('cryptography')
hiddenimports += collect_submodules('apscheduler')
hiddenimports += collect_submodules('google.genai')
hiddenimports += collect_submodules('openai')
hiddenimports += collect_submodules('httpx')
hiddenimports += collect_submodules('requests')

# 添加特定的隐藏导入
hiddenimports += [
    'uvicorn.lifespan.on',
    'uvicorn.lifespan.off',
    'uvicorn.protocols.websockets.auto',
    'uvicorn.protocols.http.auto',
    'uvicorn.protocols.http.h11_impl',
    'uvicorn.protocols.http.httptools_impl',
    'uvicorn.protocols.websockets.websockets_impl',
    'uvicorn.protocols.websockets.wsproto_impl',
    'uvicorn.loops.auto',
    'uvicorn.loops.asyncio',
    'uvicorn.loops.uvloop',
    'sqlalchemy.dialects.sqlite',
    'sqlalchemy.dialects.mysql',
    'aiosqlite',
    'aiomysql',
    'pymysql',
    'pymysql.cursors',
    'email.mime.multipart',
    'email.mime.text',
    'email.mime.base',
    'multipart',
    'multipart.multipart',
]

block_cipher = None

a = Analysis(
    [os.path.join(project_root, 'app', 'main.py')],
    pathex=[project_root],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='gemini-balance',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=os.path.join(project_root, 'app', 'static', 'icons', 'logo.png') if os.path.exists(os.path.join(project_root, 'app', 'static', 'icons', 'logo.png')) else None,
)
