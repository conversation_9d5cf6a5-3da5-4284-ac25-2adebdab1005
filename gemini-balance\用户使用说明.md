# Gemini Balance 用户使用说明

## 快速开始

### 1. 获取应用程序
- 从开发者处获取 `release` 文件夹
- 或者下载预编译的发布包

### 2. 解压和放置
将 `release` 文件夹解压到您希望安装的位置，例如：
- Windows: `C:\Program Files\GeminiBalance\`
- Linux/macOS: `/opt/gemini-balance/` 或 `~/Applications/gemini-balance/`

### 3. 首次配置

#### 使用启动器配置（推荐）
1. **Windows**: 双击 `启动器.bat`
2. **Linux/macOS**: 在终端运行 `./启动器.sh`
3. 选择选项 `[2] 配置设置`
4. 编辑配置文件，填入必要信息

#### 手动配置
1. 进入 `config` 目录
2. 将 `.env.template` 重命名为 `.env`
3. 使用文本编辑器打开 `.env` 文件
4. 根据下面的配置说明填写参数

### 4. 启动应用
- **使用启动器**: 选择 `[1] 启动 Gemini Balance 服务`
- **直接启动**: 双击 `gemini-balance.exe`（Windows）或运行 `./gemini-balance`（Linux/macOS）

### 5. 访问界面
在浏览器中打开：`http://localhost:8001`

## 配置说明

### 基本配置项

```env
# API认证令牌 - 用于访问管理界面的密码
AUTH_TOKEN=your_secure_password_here

# 数据库配置 - 数据存储位置
DATABASE_URL=sqlite:///./data/gemini_balance.db

# 服务器配置
HOST=0.0.0.0          # 监听地址，0.0.0.0 表示所有网络接口
PORT=8001              # 服务端口号
```

### 配置项详解

#### AUTH_TOKEN
- **作用**: 访问管理界面的认证令牌
- **建议**: 使用复杂的密码，包含字母、数字和特殊字符
- **示例**: `AUTH_TOKEN=MySecurePassword123!`

#### DATABASE_URL
- **SQLite（推荐）**: `sqlite:///./data/gemini_balance.db`
- **MySQL**: `mysql://用户名:密码@服务器地址/数据库名`
- **注意**: SQLite 无需额外安装，MySQL 需要单独配置

#### HOST 和 PORT
- **本地访问**: `HOST=127.0.0.1`（仅本机可访问）
- **网络访问**: `HOST=0.0.0.0`（局域网内可访问）
- **端口**: 确保选择的端口未被其他程序占用

## 功能使用

### 主要功能
1. **API 密钥管理**: 添加、删除、监控 Gemini API 密钥
2. **余额查询**: 实时查看各密钥的余额状态
3. **使用统计**: 查看 API 使用情况和历史记录
4. **自动监控**: 定期检查密钥状态和余额变化

### 界面操作
1. **登录**: 使用配置的 `AUTH_TOKEN` 登录
2. **添加密钥**: 在密钥管理页面添加新的 API 密钥
3. **查看状态**: 在状态页面查看所有密钥的实时状态
4. **查看日志**: 在日志页面查看系统运行日志

## 启动器功能

启动器提供了便捷的管理功能：

### [1] 启动服务
- 自动检查配置文件
- 启动 Gemini Balance 服务
- 显示访问地址

### [2] 配置设置
- 自动创建配置文件
- 打开编辑器修改配置
- 验证配置格式

### [3] 查看日志
- 列出所有日志文件
- 查看最新日志内容
- 帮助排查问题

### [4] 检查服务状态
- 检查端口占用情况
- 查看进程运行状态
- 确认服务是否正常

### [5] 停止服务
- 安全停止运行中的服务
- 释放端口和资源

## 常见问题

### Q: 无法访问 http://localhost:8001
**A**: 检查以下几点：
1. 服务是否正常启动（查看控制台输出）
2. 端口 8001 是否被其他程序占用
3. 防火墙是否阻止了端口访问
4. 尝试使用 `http://127.0.0.1:8001`

### Q: 忘记了 AUTH_TOKEN
**A**: 
1. 停止服务
2. 编辑 `.env` 文件，修改 `AUTH_TOKEN`
3. 重新启动服务

### Q: 数据库文件损坏
**A**:
1. 停止服务
2. 备份 `data/gemini_balance.db`
3. 删除损坏的数据库文件
4. 重新启动服务（会自动创建新数据库）

### Q: 服务启动后立即退出
**A**:
1. 查看日志文件了解错误原因
2. 检查配置文件格式是否正确
3. 确保数据目录有写入权限

### Q: 如何在其他电脑访问
**A**:
1. 确保 `HOST=0.0.0.0`
2. 在浏览器中使用服务器的 IP 地址：`http://服务器IP:8001`
3. 确保防火墙允许端口 8001 的访问

## 安全建议

### 1. 密码安全
- 使用强密码作为 `AUTH_TOKEN`
- 定期更换认证令牌
- 不要在公共场所暴露密码

### 2. 网络安全
- 如果只需本地访问，设置 `HOST=127.0.0.1`
- 在公网环境中使用 HTTPS（需要反向代理）
- 定期检查访问日志

### 3. 数据安全
- 定期备份数据库文件
- 保护 API 密钥的安全
- 不要在不安全的网络中传输敏感信息

## 维护和更新

### 日常维护
1. 定期查看日志文件
2. 监控服务运行状态
3. 备份重要数据

### 版本更新
1. 停止当前服务
2. 备份配置文件和数据
3. 替换新版本的可执行文件
4. 恢复配置和数据
5. 重新启动服务

### 数据备份
重要文件：
- `.env` - 配置文件
- `data/gemini_balance.db` - 数据库文件
- `logs/` - 日志文件

## 技术支持

如果遇到问题：
1. 查看本文档的常见问题部分
2. 检查日志文件获取详细错误信息
3. 确认配置文件格式正确
4. 联系技术支持或查看项目文档

---

**提示**: 建议在使用前仔细阅读本说明文档，确保正确配置和使用应用程序。
