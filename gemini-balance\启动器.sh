#!/bin/bash

show_menu() {
    clear
    echo "========================================"
    echo "        Gemini Balance 启动器"
    echo "========================================"
    echo
    echo "请选择操作:"
    echo
    echo "[1] 启动 Gemini Balance 服务"
    echo "[2] 配置设置"
    echo "[3] 查看日志"
    echo "[4] 检查服务状态"
    echo "[5] 停止服务"
    echo "[0] 退出"
    echo
    read -p "请输入选项 (0-5): " choice
}

start_service() {
    clear
    echo "========================================"
    echo "        启动 Gemini Balance 服务"
    echo "========================================"
    echo

    # 检查配置文件
    if [ ! -f ".env" ]; then
        if [ -f "config/.env.template" ]; then
            echo "[警告] 未找到配置文件 .env"
            echo "[信息] 发现配置模板文件，正在复制..."
            cp config/.env.template .env
            echo "[成功] 已创建配置文件 .env"
            echo "[重要] 请先编辑 .env 文件配置您的参数！"
            echo
            read -p "是否现在编辑配置文件? (y/n): " edit_config
            if [[ "$edit_config" =~ ^[Yy]$ ]]; then
                ${EDITOR:-nano} .env
            fi
            echo
        else
            echo "[错误] 未找到配置文件，请先运行配置设置"
            read -p "按回车继续..."
            return
        fi
    fi

    echo "[信息] 正在启动服务..."
    echo "[信息] 服务地址: http://localhost:8001"
    echo "[信息] 按 Ctrl+C 停止服务"
    echo

    # 检查可执行文件
    if [ -f "gemini-balance" ]; then
        ./gemini-balance
    elif [ -f "release/gemini-balance" ]; then
        cd release
        ./gemini-balance
        cd ..
    else
        echo "[错误] 未找到 gemini-balance 可执行文件"
        echo "[提示] 请先运行打包脚本生成可执行文件"
    fi

    read -p "按回车继续..."
}

config_settings() {
    clear
    echo "========================================"
    echo "           配置设置"
    echo "========================================"
    echo

    if [ ! -f ".env" ]; then
        if [ -f "config/.env.template" ]; then
            cp config/.env.template .env
            echo "[信息] 已从模板创建配置文件"
        else
            cat > .env << EOF
# Gemini Balance 配置文件
# 请根据需要修改以下配置

AUTH_TOKEN=your_auth_token_here
DATABASE_URL=sqlite:///./data/gemini_balance.db
HOST=0.0.0.0
PORT=8001
EOF
            echo "[信息] 已创建默认配置文件"
        fi
    fi

    echo "[信息] 正在打开配置文件编辑器..."
    ${EDITOR:-nano} .env

    echo
    echo "[完成] 配置文件编辑完成"
    read -p "按回车继续..."
}

view_logs() {
    clear
    echo "========================================"
    echo "           查看日志"
    echo "========================================"
    echo

    if [ -d "logs" ] && [ "$(ls -A logs/*.log 2>/dev/null)" ]; then
        echo "[信息] 日志文件列表:"
        ls -1 logs/*.log 2>/dev/null | xargs -n1 basename
        echo
        read -p "请输入要查看的日志文件名 (或按回车查看最新日志): " log_file
        if [ -z "$log_file" ]; then
            latest_log=$(ls -t logs/*.log 2>/dev/null | head -n1)
        else
            latest_log="logs/$log_file"
        fi
        if [ -f "$latest_log" ]; then
            echo "[信息] 显示日志文件: $(basename $latest_log)"
            echo "----------------------------------------"
            cat "$latest_log"
        else
            echo "[错误] 日志文件不存在: $log_file"
        fi
    elif [ -f "gemini-balance.log" ]; then
        echo "[信息] 显示主日志文件:"
        echo "----------------------------------------"
        cat gemini-balance.log
    else
        echo "[信息] 未找到日志文件"
    fi

    echo
    read -p "按回车继续..."
}

check_status() {
    clear
    echo "========================================"
    echo "           检查服务状态"
    echo "========================================"
    echo

    echo "[信息] 检查端口 8001 是否被占用..."
    if netstat -an 2>/dev/null | grep -q ":8001"; then
        echo "[状态] 服务正在运行 (端口 8001 已占用)"
        echo "[访问] http://localhost:8001"
    elif ss -an 2>/dev/null | grep -q ":8001"; then
        echo "[状态] 服务正在运行 (端口 8001 已占用)"
        echo "[访问] http://localhost:8001"
    else
        echo "[状态] 服务未运行 (端口 8001 空闲)"
    fi

    echo
    echo "[信息] 检查进程..."
    if pgrep -f "gemini-balance" > /dev/null; then
        echo "[进程] 发现 gemini-balance 进程"
        pgrep -f "gemini-balance" | while read pid; do
            echo "PID: $pid"
        done
    else
        echo "[进程] 未发现 gemini-balance 进程"
    fi

    echo
    read -p "按回车继续..."
}

stop_service() {
    clear
    echo "========================================"
    echo "           停止服务"
    echo "========================================"
    echo

    echo "[信息] 正在停止 Gemini Balance 服务..."
    if pkill -f "gemini-balance"; then
        echo "[成功] 服务已停止"
    else
        echo "[信息] 未发现运行中的服务"
    fi

    echo
    read -p "按回车继续..."
}

# 主循环
while true; do
    show_menu
    case $choice in
        1) start_service ;;
        2) config_settings ;;
        3) view_logs ;;
        4) check_status ;;
        5) stop_service ;;
        0) 
            echo
            echo "感谢使用 Gemini Balance！"
            echo
            exit 0
            ;;
        *) 
            echo "无效选项，请重新选择"
            read -p "按回车继续..."
            ;;
    esac
done
