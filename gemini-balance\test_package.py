#!/usr/bin/env python3
"""
Gemini Balance 打包测试脚本
用于验证打包前的环境和依赖项
"""

import sys
import os
import subprocess
import importlib.util

def check_python_version():
    """检查Python版本"""
    print("检查Python版本...")
    version = sys.version_info
    print(f"当前Python版本: {version.major}.{version.minor}.{version.micro}")
    
    if version.major < 3 or (version.major == 3 and version.minor < 8):
        print("❌ Python版本过低，需要Python 3.8或更高版本")
        return False
    else:
        print("✅ Python版本符合要求")
        return True

def check_required_modules():
    """检查必需的模块"""
    print("\n检查必需的模块...")
    
    required_modules = [
        'fastapi',
        'uvicorn',
        'pydantic',
        'sqlalchemy',
        'aiosqlite',
        'httpx',
        'requests',
        'jinja2',
        'python_multipart',
        'cryptography',
        'apscheduler',
        'python_dotenv'
    ]
    
    missing_modules = []
    
    for module in required_modules:
        try:
            if module == 'python_multipart':
                import multipart
            elif module == 'python_dotenv':
                import dotenv
            else:
                __import__(module)
            print(f"✅ {module}")
        except ImportError:
            print(f"❌ {module} - 未安装")
            missing_modules.append(module)
    
    if missing_modules:
        print(f"\n缺少以下模块: {', '.join(missing_modules)}")
        print("请运行: pip install -r requirements.txt")
        return False
    else:
        print("✅ 所有必需模块已安装")
        return True

def check_project_structure():
    """检查项目结构"""
    print("\n检查项目结构...")
    
    required_files = [
        'app/main.py',
        'app/core/application.py',
        'requirements.txt',
        'gemini-balance.spec'
    ]
    
    required_dirs = [
        'app',
        'app/static',
        'app/templates'
    ]
    
    missing_items = []
    
    # 检查文件
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} - 文件不存在")
            missing_items.append(file_path)
    
    # 检查目录
    for dir_path in required_dirs:
        if os.path.isdir(dir_path):
            print(f"✅ {dir_path}/")
        else:
            print(f"❌ {dir_path}/ - 目录不存在")
            missing_items.append(dir_path)
    
    if missing_items:
        print(f"\n缺少以下文件或目录: {', '.join(missing_items)}")
        return False
    else:
        print("✅ 项目结构完整")
        return True

def check_pyinstaller():
    """检查PyInstaller是否可用"""
    print("\n检查PyInstaller...")
    
    try:
        import PyInstaller
        print(f"✅ PyInstaller已安装 (版本: {PyInstaller.__version__})")
        return True
    except ImportError:
        print("❌ PyInstaller未安装")
        print("请运行: pip install pyinstaller")
        return False

def test_app_import():
    """测试应用程序导入"""
    print("\n测试应用程序导入...")
    
    try:
        # 添加项目根目录到Python路径
        project_root = os.path.dirname(os.path.abspath(__file__))
        if project_root not in sys.path:
            sys.path.insert(0, project_root)
        
        # 测试导入主要模块
        from app.core.application import create_app
        print("✅ 成功导入 create_app")
        
        # 测试创建应用
        app = create_app()
        print("✅ 成功创建应用实例")
        
        return True
    except Exception as e:
        print(f"❌ 导入失败: {e}")
        return False

def check_static_files():
    """检查静态文件"""
    print("\n检查静态文件...")
    
    static_dirs = ['app/static', 'app/templates']
    
    for static_dir in static_dirs:
        if os.path.isdir(static_dir):
            files = []
            for root, dirs, filenames in os.walk(static_dir):
                for filename in filenames:
                    files.append(os.path.relpath(os.path.join(root, filename)))
            
            if files:
                print(f"✅ {static_dir}/ ({len(files)} 个文件)")
                for file in files[:5]:  # 只显示前5个文件
                    print(f"   - {file}")
                if len(files) > 5:
                    print(f"   ... 还有 {len(files) - 5} 个文件")
            else:
                print(f"⚠️  {static_dir}/ (空目录)")
        else:
            print(f"❌ {static_dir}/ - 目录不存在")

def run_build_test():
    """运行构建测试"""
    print("\n运行构建测试...")
    
    try:
        # 测试PyInstaller命令
        result = subprocess.run([
            'pyinstaller', '--version'
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print(f"✅ PyInstaller命令可用: {result.stdout.strip()}")
            return True
        else:
            print(f"❌ PyInstaller命令失败: {result.stderr}")
            return False
    except subprocess.TimeoutExpired:
        print("❌ PyInstaller命令超时")
        return False
    except FileNotFoundError:
        print("❌ 找不到PyInstaller命令")
        return False
    except Exception as e:
        print(f"❌ 运行PyInstaller命令时出错: {e}")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("    Gemini Balance 打包环境检查")
    print("=" * 50)
    
    checks = [
        ("Python版本", check_python_version),
        ("必需模块", check_required_modules),
        ("项目结构", check_project_structure),
        ("PyInstaller", check_pyinstaller),
        ("应用导入", test_app_import),
        ("静态文件", check_static_files),
        ("构建测试", run_build_test)
    ]
    
    results = []
    
    for name, check_func in checks:
        try:
            result = check_func()
            results.append((name, result))
        except Exception as e:
            print(f"❌ {name} 检查时出错: {e}")
            results.append((name, False))
    
    # 总结
    print("\n" + "=" * 50)
    print("    检查结果总结")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 项检查通过")
    
    if passed == total:
        print("\n🎉 所有检查通过！可以开始打包。")
        print("运行以下命令开始打包:")
        if os.name == 'nt':  # Windows
            print("  build_exe.bat")
        else:  # Linux/macOS
            print("  ./build_exe.sh")
    else:
        print(f"\n⚠️  有 {total - passed} 项检查失败，请先解决这些问题。")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
