@echo off
chcp 65001 >nul
title Gemini Balance 启动器

:menu
cls
echo ========================================
echo        Gemini Balance 启动器
echo ========================================
echo.
echo 请选择操作:
echo.
echo [1] 启动 Gemini Balance 服务
echo [2] 配置设置
echo [3] 查看日志
echo [4] 检查服务状态
echo [5] 停止服务
echo [0] 退出
echo.
set /p choice="请输入选项 (0-5): "

if "%choice%"=="1" goto start_service
if "%choice%"=="2" goto config
if "%choice%"=="3" goto view_logs
if "%choice%"=="4" goto check_status
if "%choice%"=="5" goto stop_service
if "%choice%"=="0" goto exit
goto menu

:start_service
cls
echo ========================================
echo        启动 Gemini Balance 服务
echo ========================================
echo.

:: 检查配置文件
if not exist ".env" (
    if exist "config\.env.template" (
        echo [警告] 未找到配置文件 .env
        echo [信息] 发现配置模板文件，正在复制...
        copy config\.env.template .env >nul
        echo [成功] 已创建配置文件 .env
        echo [重要] 请先编辑 .env 文件配置您的参数！
        echo.
        set /p edit_config="是否现在编辑配置文件? (y/n): "
        if /i "%edit_config%"=="y" (
            notepad .env
        )
        echo.
    ) else (
        echo [错误] 未找到配置文件，请先运行配置设置
        pause
        goto menu
    )
)

echo [信息] 正在启动服务...
echo [信息] 服务地址: http://localhost:8001
echo [信息] 按 Ctrl+C 停止服务
echo.

:: 检查exe文件
if exist "gemini-balance.exe" (
    gemini-balance.exe
) else if exist "release\gemini-balance.exe" (
    cd release
    gemini-balance.exe
    cd ..
) else (
    echo [错误] 未找到 gemini-balance.exe 文件
    echo [提示] 请先运行打包脚本生成exe文件
)

pause
goto menu

:config
cls
echo ========================================
echo           配置设置
echo ========================================
echo.

if not exist ".env" (
    if exist "config\.env.template" (
        copy config\.env.template .env >nul
        echo [信息] 已从模板创建配置文件
    ) else (
        echo # Gemini Balance 配置文件 > .env
        echo # 请根据需要修改以下配置 >> .env
        echo. >> .env
        echo AUTH_TOKEN=your_auth_token_here >> .env
        echo DATABASE_URL=sqlite:///./data/gemini_balance.db >> .env
        echo HOST=0.0.0.0 >> .env
        echo PORT=8001 >> .env
        echo [信息] 已创建默认配置文件
    )
)

echo [信息] 正在打开配置文件编辑器...
notepad .env

echo.
echo [完成] 配置文件编辑完成
pause
goto menu

:view_logs
cls
echo ========================================
echo           查看日志
echo ========================================
echo.

if exist "logs" (
    echo [信息] 日志文件列表:
    dir /b logs\*.log 2>nul
    echo.
    set /p log_file="请输入要查看的日志文件名 (或按回车查看最新日志): "
    if "%log_file%"=="" (
        for /f %%i in ('dir /b /o-d logs\*.log 2^>nul') do (
            set latest_log=%%i
            goto show_log
        )
    ) else (
        set latest_log=%log_file%
    )
    :show_log
    if exist "logs\%latest_log%" (
        echo [信息] 显示日志文件: %latest_log%
        echo ----------------------------------------
        type logs\%latest_log%
    ) else (
        echo [错误] 日志文件不存在: %latest_log%
    )
) else if exist "gemini-balance.log" (
    echo [信息] 显示主日志文件:
    echo ----------------------------------------
    type gemini-balance.log
) else (
    echo [信息] 未找到日志文件
)

echo.
pause
goto menu

:check_status
cls
echo ========================================
echo           检查服务状态
echo ========================================
echo.

echo [信息] 检查端口 8001 是否被占用...
netstat -an | find ":8001" >nul
if %errorlevel%==0 (
    echo [状态] 服务正在运行 (端口 8001 已占用)
    echo [访问] http://localhost:8001
) else (
    echo [状态] 服务未运行 (端口 8001 空闲)
)

echo.
echo [信息] 检查进程...
tasklist | find /i "gemini-balance" >nul
if %errorlevel%==0 (
    echo [进程] 发现 gemini-balance 进程
    tasklist | find /i "gemini-balance"
) else (
    echo [进程] 未发现 gemini-balance 进程
)

echo.
pause
goto menu

:stop_service
cls
echo ========================================
echo           停止服务
echo ========================================
echo.

echo [信息] 正在停止 Gemini Balance 服务...
taskkill /f /im gemini-balance.exe >nul 2>&1
if %errorlevel%==0 (
    echo [成功] 服务已停止
) else (
    echo [信息] 未发现运行中的服务
)

echo.
pause
goto menu

:exit
echo.
echo 感谢使用 Gemini Balance！
echo.
pause
exit
