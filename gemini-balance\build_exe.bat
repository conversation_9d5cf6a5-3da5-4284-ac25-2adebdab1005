@echo off
chcp 65001 >nul
echo ========================================
echo     Gemini Balance EXE 打包工具
echo ========================================
echo.

:: 检查Python是否安装
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo [错误] 未检测到Python，请先安装Python 3.8或更高版本
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo [信息] 检测到Python版本:
python --version

:: 创建虚拟环境
echo.
echo [步骤1] 创建虚拟环境...
if exist "build_env" (
    echo [信息] 删除现有的虚拟环境...
    rmdir /s /q build_env
)
python -m venv build_env
if %errorlevel% neq 0 (
    echo [错误] 创建虚拟环境失败
    pause
    exit /b 1
)

:: 激活虚拟环境
echo [步骤2] 激活虚拟环境...
call build_env\Scripts\activate.bat

:: 升级pip
echo [步骤3] 升级pip...
python -m pip install --upgrade pip

:: 安装项目依赖
echo [步骤4] 安装项目依赖...
pip install -r requirements.txt
if %errorlevel% neq 0 (
    echo [错误] 安装项目依赖失败
    pause
    exit /b 1
)

:: 安装PyInstaller
echo [步骤5] 安装PyInstaller...
pip install pyinstaller
if %errorlevel% neq 0 (
    echo [错误] 安装PyInstaller失败
    pause
    exit /b 1
)

:: 清理之前的构建
echo [步骤6] 清理之前的构建文件...
if exist "dist" rmdir /s /q dist
if exist "build" rmdir /s /q build

:: 使用PyInstaller打包
echo [步骤7] 开始打包exe文件...
echo [信息] 这可能需要几分钟时间，请耐心等待...
pyinstaller gemini-balance.spec --clean
if %errorlevel% neq 0 (
    echo [错误] 打包失败，请检查错误信息
    pause
    exit /b 1
)

:: 创建发布目录
echo [步骤8] 创建发布包...
if exist "release" rmdir /s /q release
mkdir release
mkdir release\config
mkdir release\data
mkdir release\logs

:: 复制exe文件
copy dist\gemini-balance.exe release\
if %errorlevel% neq 0 (
    echo [错误] 复制exe文件失败
    pause
    exit /b 1
)

:: 复制配置文件模板
if exist ".env" (
    copy .env release\config\.env.template
) else (
    echo # Gemini Balance 配置文件模板 > release\config\.env.template
    echo # 请根据需要修改以下配置 >> release\config\.env.template
    echo. >> release\config\.env.template
    echo # API认证令牌 >> release\config\.env.template
    echo AUTH_TOKEN=your_auth_token_here >> release\config\.env.template
    echo. >> release\config\.env.template
    echo # 数据库配置 >> release\config\.env.template
    echo DATABASE_URL=sqlite:///./data/gemini_balance.db >> release\config\.env.template
    echo. >> release\config\.env.template
    echo # 服务器配置 >> release\config\.env.template
    echo HOST=0.0.0.0 >> release\config\.env.template
    echo PORT=8001 >> release\config\.env.template
)

:: 复制数据库文件（如果存在）
if exist "data\gemini_balance.db" (
    copy data\gemini_balance.db release\data\
)

echo.
echo ========================================
echo           打包完成！
echo ========================================
echo.
echo [成功] exe文件已生成到 release 目录
echo [文件] release\gemini-balance.exe
echo [配置] release\config\.env.template
echo.
echo 使用说明:
echo 1. 将 release 文件夹复制到目标电脑
echo 2. 修改 config\.env.template 为 .env 并配置参数
echo 3. 双击运行 gemini-balance.exe
echo 4. 浏览器访问 http://localhost:8001
echo.
echo 按任意键退出...
pause >nul
