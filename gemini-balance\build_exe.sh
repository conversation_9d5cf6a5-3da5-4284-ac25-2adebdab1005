#!/bin/bash

echo "========================================"
echo "     Gemini Balance EXE 打包工具"
echo "========================================"
echo

# 检查Python是否安装
if ! command -v python3 &> /dev/null; then
    echo "[错误] 未检测到Python3，请先安装Python 3.8或更高版本"
    exit 1
fi

echo "[信息] 检测到Python版本:"
python3 --version

# 创建虚拟环境
echo
echo "[步骤1] 创建虚拟环境..."
if [ -d "build_env" ]; then
    echo "[信息] 删除现有的虚拟环境..."
    rm -rf build_env
fi
python3 -m venv build_env
if [ $? -ne 0 ]; then
    echo "[错误] 创建虚拟环境失败"
    exit 1
fi

# 激活虚拟环境
echo "[步骤2] 激活虚拟环境..."
source build_env/bin/activate

# 升级pip
echo "[步骤3] 升级pip..."
python -m pip install --upgrade pip

# 安装项目依赖
echo "[步骤4] 安装项目依赖..."
pip install -r requirements.txt
if [ $? -ne 0 ]; then
    echo "[错误] 安装项目依赖失败"
    exit 1
fi

# 安装PyInstaller
echo "[步骤5] 安装PyInstaller..."
pip install pyinstaller
if [ $? -ne 0 ]; then
    echo "[错误] 安装PyInstaller失败"
    exit 1
fi

# 清理之前的构建
echo "[步骤6] 清理之前的构建文件..."
rm -rf dist build

# 使用PyInstaller打包
echo "[步骤7] 开始打包exe文件..."
echo "[信息] 这可能需要几分钟时间，请耐心等待..."
pyinstaller gemini-balance.spec --clean
if [ $? -ne 0 ]; then
    echo "[错误] 打包失败，请检查错误信息"
    exit 1
fi

# 创建发布目录
echo "[步骤8] 创建发布包..."
rm -rf release
mkdir -p release/config
mkdir -p release/data
mkdir -p release/logs

# 复制exe文件
cp dist/gemini-balance release/
if [ $? -ne 0 ]; then
    echo "[错误] 复制exe文件失败"
    exit 1
fi

# 设置执行权限
chmod +x release/gemini-balance

# 复制配置文件模板
if [ -f ".env" ]; then
    cp .env release/config/.env.template
else
    cat > release/config/.env.template << EOF
# Gemini Balance 配置文件模板
# 请根据需要修改以下配置

# API认证令牌
AUTH_TOKEN=your_auth_token_here

# 数据库配置
DATABASE_URL=sqlite:///./data/gemini_balance.db

# 服务器配置
HOST=0.0.0.0
PORT=8001
EOF
fi

# 复制数据库文件（如果存在）
if [ -f "data/gemini_balance.db" ]; then
    cp data/gemini_balance.db release/data/
fi

echo
echo "========================================"
echo "           打包完成！"
echo "========================================"
echo
echo "[成功] 可执行文件已生成到 release 目录"
echo "[文件] release/gemini-balance"
echo "[配置] release/config/.env.template"
echo
echo "使用说明:"
echo "1. 将 release 文件夹复制到目标电脑"
echo "2. 修改 config/.env.template 为 .env 并配置参数"
echo "3. 运行 ./gemini-balance"
echo "4. 浏览器访问 http://localhost:8001"
echo
