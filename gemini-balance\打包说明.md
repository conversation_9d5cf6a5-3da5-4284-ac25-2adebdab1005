# Gemini Balance EXE 打包说明

## 概述

本文档详细说明如何将 Gemini Balance 项目打包成独立的可执行文件（exe），使其能够在任何电脑上运行，无需安装 Python 环境。

## 系统要求

### 开发环境要求（用于打包）
- Python 3.8 或更高版本
- Windows 10/11 或 Linux/macOS
- 至少 2GB 可用磁盘空间
- 稳定的网络连接（用于下载依赖）

### 目标运行环境要求
- Windows 7 或更高版本（Windows exe）
- Linux x64（Linux 可执行文件）
- macOS 10.14 或更高版本（macOS 可执行文件）
- 至少 512MB 可用内存
- 至少 200MB 可用磁盘空间

## 打包步骤

### Windows 系统

1. **准备环境**
   ```bash
   # 确保在项目根目录
   cd gemini-balance
   ```

2. **运行打包脚本**
   ```bash
   # 双击运行或在命令行执行
   build_exe.bat
   ```

3. **等待打包完成**
   - 脚本会自动创建虚拟环境
   - 安装所有依赖项
   - 使用 PyInstaller 打包
   - 创建发布包

### Linux/macOS 系统

1. **设置执行权限**
   ```bash
   chmod +x build_exe.sh
   ```

2. **运行打包脚本**
   ```bash
   ./build_exe.sh
   ```

## 打包输出

打包完成后，会在项目根目录生成以下结构：

```
release/
├── gemini-balance.exe          # 主程序（Windows）
├── gemini-balance              # 主程序（Linux/macOS）
├── config/
│   └── .env.template          # 配置文件模板
├── data/                      # 数据目录
│   └── gemini_balance.db      # 数据库文件（如果存在）
└── logs/                      # 日志目录
```

## 部署说明

### 1. 复制文件
将整个 `release` 文件夹复制到目标电脑的任意位置。

### 2. 配置应用
1. 进入 `release/config` 目录
2. 将 `.env.template` 重命名为 `.env`
3. 编辑 `.env` 文件，配置必要参数：

```env
# API认证令牌
AUTH_TOKEN=your_auth_token_here

# 数据库配置
DATABASE_URL=sqlite:///./data/gemini_balance.db

# 服务器配置
HOST=0.0.0.0
PORT=8001
```

### 3. 启动应用

#### 方法一：直接运行
- Windows: 双击 `gemini-balance.exe`
- Linux/macOS: 在终端运行 `./gemini-balance`

#### 方法二：使用启动器（推荐）
- Windows: 双击 `启动器.bat`
- Linux/macOS: 运行 `./启动器.sh`

启动器提供以下功能：
- 启动/停止服务
- 配置管理
- 日志查看
- 状态检查

### 4. 访问应用
启动成功后，在浏览器中访问：`http://localhost:8001`

## 故障排除

### 常见问题

1. **启动失败**
   - 检查配置文件是否正确
   - 确保端口 8001 未被占用
   - 查看日志文件获取详细错误信息

2. **配置文件问题**
   - 确保 `.env` 文件存在于正确位置
   - 检查配置参数格式是否正确
   - 确保必要的配置项已填写

3. **数据库问题**
   - 确保 `data` 目录存在且可写
   - 检查数据库文件权限
   - 如果数据库损坏，删除后重新启动会自动创建

4. **网络问题**
   - 检查防火墙设置
   - 确保端口 8001 未被阻止
   - 尝试使用 `127.0.0.1:8001` 访问

### 日志查看

日志文件位置：
- 主日志：`logs/` 目录下的 `.log` 文件
- 错误日志：控制台输出或 `gemini-balance.log`

使用启动器的"查看日志"功能可以方便地查看日志内容。

## 高级配置

### 自定义端口
在 `.env` 文件中修改 `PORT` 参数：
```env
PORT=9000
```

### 数据库配置
支持 SQLite 和 MySQL：
```env
# SQLite（默认）
DATABASE_URL=sqlite:///./data/gemini_balance.db

# MySQL
DATABASE_URL=mysql://username:password@localhost/gemini_balance
```

### 安全配置
建议在生产环境中：
1. 使用强密码作为 `AUTH_TOKEN`
2. 限制访问 IP（修改 `HOST` 参数）
3. 使用 HTTPS（需要反向代理）

## 技术细节

### 打包工具
- **PyInstaller**: 用于将 Python 应用打包成可执行文件
- **规格文件**: `gemini-balance.spec` 定义了打包参数

### 包含的组件
- FastAPI Web 框架
- SQLAlchemy 数据库 ORM
- Uvicorn ASGI 服务器
- 所有项目依赖项
- 静态文件和模板
- 数据库文件（如果存在）

### 性能优化
- 使用 UPX 压缩减小文件大小
- 排除不必要的模块
- 优化启动时间

## 更新说明

当项目代码更新时：
1. 重新运行打包脚本
2. 备份现有配置和数据
3. 替换可执行文件
4. 恢复配置和数据

## 支持

如果遇到问题：
1. 查看本文档的故障排除部分
2. 检查项目的 GitHub Issues
3. 查看日志文件获取详细错误信息
4. 联系项目维护者

## 自动化脚本说明

### build_exe.bat / build_exe.sh
自动化打包脚本，执行以下操作：
1. 检查 Python 环境
2. 创建虚拟环境
3. 安装依赖项
4. 运行 PyInstaller
5. 创建发布包
6. 复制配置文件

### 启动器.bat / 启动器.sh
用户友好的启动管理工具：
- 服务启动/停止
- 配置文件管理
- 日志查看
- 状态监控

### gemini-balance.spec
PyInstaller 配置文件，定义：
- 入口点文件
- 依赖项收集
- 静态文件包含
- 隐藏导入模块
- 输出配置

---

**注意**: 打包后的可执行文件仅适用于打包时的操作系统架构。如需支持其他平台，请在对应平台上重新打包。
